"""
安全管理路由
用于查看和管理安全事件、被阻止的IP等
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, current_app
from flask_login import login_required, current_user
from app.security_config import (
    get_blocked_ips, unblock_ip, block_ip, ip_access_log,
    get_whitelist_ips, add_to_whitelist, remove_from_whitelist, is_whitelisted
)
from app import db
from sqlalchemy import text
import json
from datetime import datetime

security_admin_bp = Blueprint('security_admin', __name__)

@security_admin_bp.route('/security/dashboard')
@login_required
def dashboard():
    """安全仪表盘"""
    # 暂时移除权限检查，所有登录用户都可以访问
    # if not current_user.is_admin():
    #     flash('您没有权限访问此页面', 'error')
    #     return redirect(url_for('main.index'))

    blocked_ips = get_blocked_ips()

    # 获取访问统计
    access_stats = {}
    for ip, requests in ip_access_log.items():
        access_stats[ip] = {
            'count': len(requests),
            'last_access': max(requests) if requests else 0
        }

    # 按访问次数排序
    top_ips = sorted(access_stats.items(), key=lambda x: x[1]['count'], reverse=True)[:10]

    return render_template('security/dashboard.html',
                         blocked_ips=blocked_ips,
                         top_ips=top_ips,
                         total_blocked=len(blocked_ips))

@security_admin_bp.route('/security/blocked-ips')
@login_required
def blocked_ips():
    """被阻止的IP列表"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    blocked_list = get_blocked_ips()
    return jsonify({
        'blocked_ips': blocked_list,
        'count': len(blocked_list)
    })

@security_admin_bp.route('/security/unblock-ip', methods=['POST'])
@login_required
def unblock_ip_route():
    """解除IP阻止"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    data = request.get_json()
    ip = data.get('ip')

    if not ip:
        return jsonify({'error': 'IP地址不能为空'}), 400

    unblock_ip(ip)
    return jsonify({'success': True, 'message': f'IP {ip} 已解除阻止'})

@security_admin_bp.route('/security/block-ip', methods=['POST'])
@login_required
def block_ip_route():
    """手动阻止IP"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    data = request.get_json()
    ip = data.get('ip')

    if not ip:
        return jsonify({'error': 'IP地址不能为空'}), 400

    # 简单的IP格式验证
    import ipaddress
    try:
        ipaddress.ip_address(ip)
    except ValueError:
        return jsonify({'error': 'IP地址格式无效'}), 400

    block_ip(ip)
    return jsonify({'success': True, 'message': f'IP {ip} 已被阻止'})

@security_admin_bp.route('/security/whitelist-ips')
@login_required
def whitelist_ips():
    """白名单IP列表"""
    whitelist = get_whitelist_ips()
    return jsonify({
        'whitelist_ips': whitelist,
        'count': len(whitelist)
    })

@security_admin_bp.route('/security/add-to-whitelist', methods=['POST'])
@login_required
def add_to_whitelist_route():
    """添加IP到白名单"""
    data = request.get_json()
    ip = data.get('ip')

    if not ip:
        return jsonify({'error': 'IP地址不能为空'}), 400

    # 简单的IP格式验证
    import ipaddress
    try:
        ipaddress.ip_address(ip)
    except ValueError:
        return jsonify({'error': 'IP地址格式无效'}), 400

    add_to_whitelist(ip)
    return jsonify({'success': True, 'message': f'IP {ip} 已添加到白名单'})

@security_admin_bp.route('/security/remove-from-whitelist', methods=['POST'])
@login_required
def remove_from_whitelist_route():
    """从白名单移除IP"""
    data = request.get_json()
    ip = data.get('ip')

    if not ip:
        return jsonify({'error': 'IP地址不能为空'}), 400

    remove_from_whitelist(ip)
    return jsonify({'success': True, 'message': f'IP {ip} 已从白名单移除'})

@security_admin_bp.route('/security/access-log')
@login_required
def access_log():
    """访问日志"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    # 获取访问统计
    access_stats = []
    for ip, requests in ip_access_log.items():
        if requests:
            access_stats.append({
                'ip': ip,
                'count': len(requests),
                'last_access': datetime.fromtimestamp(max(requests)).strftime('%Y-%m-%d %H:%M:%S'),
                'first_access': datetime.fromtimestamp(min(requests)).strftime('%Y-%m-%d %H:%M:%S')
            })

    # 按最后访问时间排序
    access_stats.sort(key=lambda x: x['last_access'], reverse=True)

    return jsonify({
        'access_log': access_stats[:100],  # 只返回最近100条
        'total': len(access_stats)
    })

@security_admin_bp.route('/security/clear-logs', methods=['POST'])
@login_required
def clear_logs():
    """清空访问日志"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    ip_access_log.clear()
    return jsonify({'success': True, 'message': '访问日志已清空'})

@security_admin_bp.route('/security/stats')
@login_required
def security_stats():
    """安全统计信息"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    # 统计信息
    stats = {
        'blocked_ips_count': len(get_blocked_ips()),
        'active_sessions': len(ip_access_log),
        'total_requests': sum(len(requests) for requests in ip_access_log.values()),
        'top_ips': []
    }

    # 获取访问最频繁的IP
    ip_counts = [(ip, len(requests)) for ip, requests in ip_access_log.items()]
    ip_counts.sort(key=lambda x: x[1], reverse=True)
    stats['top_ips'] = ip_counts[:5]

    return jsonify(stats)

@security_admin_bp.route('/admin/balance-stock-in-report')
@login_required
def balance_stock_in_report():
    """平账入库报告"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # 获取平账入库统计信息
        stats_sql = text("""
        SELECT
            COUNT(*) as total_count,
            COUNT(CASE WHEN MONTH(si.stock_in_date) = MONTH(GETDATE())
                       AND YEAR(si.stock_in_date) = YEAR(GETDATE()) THEN 1 END) as month_count,
            COUNT(CASE WHEN si.status = '已入库' THEN 1 END) as consumed_count,
            COUNT(DISTINCT si.supplier_id) as supplier_count
        FROM stock_ins si
        WHERE si.stock_in_type = '平账入库'
        """)

        stats_result = db.session.execute(stats_sql).fetchone()
        stats = {
            'total_count': stats_result.total_count if stats_result else 0,
            'month_count': stats_result.month_count if stats_result else 0,
            'consumed_count': stats_result.consumed_count if stats_result else 0,
            'supplier_count': stats_result.supplier_count if stats_result else 0
        }

        # 获取平账入库记录
        records_sql = text("""
        SELECT
            si.id as stock_in_id,
            si.stock_in_number,
            si.stock_in_date,
            si.status,
            si.notes,
            sii.quantity,
            sii.unit,
            i.name as ingredient_name,
            s.name as supplier_name,
            CASE WHEN si.notes LIKE '%消耗计划ID:%'
                 THEN CAST(SUBSTRING(si.notes, CHARINDEX('消耗计划ID:', si.notes) + 8,
                          CHARINDEX(',', si.notes + ',', CHARINDEX('消耗计划ID:', si.notes)) - CHARINDEX('消耗计划ID:', si.notes) - 8) AS INT)
                 ELSE NULL END as consumption_plan_id,
            CASE WHEN inv.quantity = 0 OR inv.quantity IS NULL THEN 1 ELSE 0 END as is_consumed
        FROM stock_ins si
        LEFT JOIN stock_in_items sii ON si.id = sii.stock_in_id
        LEFT JOIN ingredients i ON sii.ingredient_id = i.id
        LEFT JOIN suppliers s ON si.supplier_id = s.id
        LEFT JOIN inventories inv ON sii.batch_number = inv.batch_number
        WHERE si.stock_in_type = '平账入库'
        ORDER BY si.stock_in_date DESC
        OFFSET :offset ROWS FETCH NEXT :per_page ROWS ONLY
        """)

        offset = (page - 1) * per_page
        records_result = db.session.execute(records_sql, {
            'offset': offset,
            'per_page': per_page
        }).fetchall()

        # 转换为字典列表
        balance_records = []
        for record in records_result:
            balance_records.append({
                'stock_in_id': record.stock_in_id,
                'stock_in_number': record.stock_in_number,
                'stock_in_date': record.stock_in_date,
                'status': record.status,
                'quantity': record.quantity,
                'unit': record.unit,
                'ingredient_name': record.ingredient_name,
                'supplier_name': record.supplier_name,
                'consumption_plan_id': record.consumption_plan_id,
                'is_consumed': bool(record.is_consumed)
            })

        # 计算总页数
        total_count = stats['total_count']
        total_pages = (total_count + per_page - 1) // per_page

        # 创建分页对象
        class Pagination:
            def __init__(self, page, per_page, total, items):
                self.page = page
                self.per_page = per_page
                self.total = total
                self.items = items
                self.pages = (total + per_page - 1) // per_page
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None

            def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
                last = self.pages
                for num in range(1, last + 1):
                    if num <= left_edge or \
                       (self.page - left_current - 1 < num < self.page + right_current) or \
                       num > last - right_edge:
                        yield num

        pagination = Pagination(page, per_page, total_count, balance_records)

        return render_template('admin/balance_stock_in_report.html',
                             stats=stats,
                             balance_records=balance_records,
                             pagination=pagination)

    except Exception as e:
        current_app.logger.error(f"获取平账入库报告失败: {str(e)}")
        return render_template('admin/balance_stock_in_report.html',
                             stats={'total_count': 0, 'month_count': 0, 'consumed_count': 0, 'supplier_count': 0},
                             balance_records=[],
                             pagination=None)
