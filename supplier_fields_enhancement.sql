-- =====================================================
-- 出入库相关表供应商字段完善SQL脚本
-- 数据库: StudentsCMSSP
-- 执行前请备份数据库
-- =====================================================

USE [StudentsCMSSP]
GO

PRINT '开始检查和完善出入库相关表的供应商字段...'
PRINT '================================================'

-- =====================================================
-- 1. 检查和完善 stock_ins 表（入库单表）
-- =====================================================
PRINT '1. 检查 stock_ins 表的供应商字段...'

-- 检查 supplier_id 字段是否存在
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_ins ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 stock_ins 表'
END
ELSE
BEGIN
    PRINT '  ✓ stock_ins.supplier_id 字段已存在'
END

-- 检查外键约束是否存在
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_supplier_id')
BEGIN
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_supplier_id 
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 stock_ins.supplier_id 外键约束'
END
ELSE
BEGIN
    PRINT '  ✓ stock_ins.supplier_id 外键约束已存在'
END

-- =====================================================
-- 2. 检查和完善 stock_in_items 表（入库明细表）
-- =====================================================
PRINT '2. 检查 stock_in_items 表的供应商字段...'

-- 检查 supplier_id 字段是否存在
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_in_items') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_in_items ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 stock_in_items 表'
END
ELSE
BEGIN
    PRINT '  ✓ stock_in_items.supplier_id 字段已存在'
END

-- 检查外键约束是否存在
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_in_items_supplier_id')
BEGIN
    ALTER TABLE stock_in_items ADD CONSTRAINT FK_stock_in_items_supplier_id 
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 stock_in_items.supplier_id 外键约束'
END
ELSE
BEGIN
    PRINT '  ✓ stock_in_items.supplier_id 外键约束已存在'
END

-- =====================================================
-- 3. 检查和完善 inventories 表（库存表）
-- =====================================================
PRINT '3. 检查 inventories 表的供应商字段...'

-- 检查 supplier_id 字段是否存在
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inventories') AND name = 'supplier_id')
BEGIN
    ALTER TABLE inventories ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 inventories 表'
END
ELSE
BEGIN
    PRINT '  ✓ inventories.supplier_id 字段已存在'
END

-- 检查外键约束是否存在
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_inventories_supplier_id')
BEGIN
    ALTER TABLE inventories ADD CONSTRAINT FK_inventories_supplier_id 
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 inventories.supplier_id 外键约束'
END
ELSE
BEGIN
    PRINT '  ✓ inventories.supplier_id 外键约束已存在'
END

-- =====================================================
-- 4. 检查和完善 stock_outs 表（出库单表）
-- =====================================================
PRINT '4. 检查 stock_outs 表的供应商相关字段...'

-- 出库单表通常不直接关联供应商，但可以添加主要供应商字段用于快速查询
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id')
BEGIN
    ALTER TABLE stock_outs ADD main_supplier_id INT NULL;
    PRINT '  ✓ 添加 main_supplier_id 字段到 stock_outs 表（主要供应商）'
END
ELSE
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 字段已存在'
END

-- 检查外键约束是否存在
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_outs_main_supplier_id')
BEGIN
    ALTER TABLE stock_outs ADD CONSTRAINT FK_stock_outs_main_supplier_id 
        FOREIGN KEY (main_supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 stock_outs.main_supplier_id 外键约束'
END
ELSE
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 外键约束已存在'
END

-- =====================================================
-- 5. 检查和完善 stock_out_items 表（出库明细表）
-- =====================================================
PRINT '5. 检查 stock_out_items 表的供应商相关字段...'

-- 出库明细表可以通过库存记录关联供应商，但为了查询效率可以冗余存储
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_out_items ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 stock_out_items 表（冗余字段，提高查询效率）'
END
ELSE
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 字段已存在'
END

-- 检查外键约束是否存在
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_out_items_supplier_id')
BEGIN
    ALTER TABLE stock_out_items ADD CONSTRAINT FK_stock_out_items_supplier_id 
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 stock_out_items.supplier_id 外键约束'
END
ELSE
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 外键约束已存在'
END

-- =====================================================
-- 6. 创建索引以提高查询性能
-- =====================================================
PRINT '6. 创建供应商相关索引...'

-- stock_ins 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_supplier_id' AND object_id = OBJECT_ID('stock_ins'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_stock_ins_supplier_id ON stock_ins(supplier_id);
    PRINT '  ✓ 创建 stock_ins.supplier_id 索引'
END
ELSE
BEGIN
    PRINT '  ✓ stock_ins.supplier_id 索引已存在'
END

-- stock_in_items 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_in_items_supplier_id' AND object_id = OBJECT_ID('stock_in_items'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_stock_in_items_supplier_id ON stock_in_items(supplier_id);
    PRINT '  ✓ 创建 stock_in_items.supplier_id 索引'
END
ELSE
BEGIN
    PRINT '  ✓ stock_in_items.supplier_id 索引已存在'
END

-- inventories 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_inventories_supplier_id' AND object_id = OBJECT_ID('inventories'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_inventories_supplier_id ON inventories(supplier_id);
    PRINT '  ✓ 创建 inventories.supplier_id 索引'
END
ELSE
BEGIN
    PRINT '  ✓ inventories.supplier_id 索引已存在'
END

-- stock_outs 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_outs_main_supplier_id' AND object_id = OBJECT_ID('stock_outs'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_stock_outs_main_supplier_id ON stock_outs(main_supplier_id);
    PRINT '  ✓ 创建 stock_outs.main_supplier_id 索引'
END
ELSE
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 索引已存在'
END

-- stock_out_items 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_out_items_supplier_id' AND object_id = OBJECT_ID('stock_out_items'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_stock_out_items_supplier_id ON stock_out_items(supplier_id);
    PRINT '  ✓ 创建 stock_out_items.supplier_id 索引'
END
ELSE
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 索引已存在'
END

-- =====================================================
-- 7. 数据完整性检查和修复
-- =====================================================
PRINT '7. 执行数据完整性检查和修复...'

-- 更新入库明细表的供应商信息（从入库单继承）
UPDATE sii 
SET supplier_id = si.supplier_id
FROM stock_in_items sii
JOIN stock_ins si ON sii.stock_in_id = si.id
WHERE sii.supplier_id IS NULL AND si.supplier_id IS NOT NULL;

PRINT '  ✓ 更新入库明细表的供应商信息'

-- 更新库存表的供应商信息（从入库明细继承）
UPDATE inv 
SET supplier_id = sii.supplier_id
FROM inventories inv
JOIN stock_in_items sii ON inv.batch_number = sii.batch_number
WHERE inv.supplier_id IS NULL AND sii.supplier_id IS NOT NULL;

PRINT '  ✓ 更新库存表的供应商信息'

-- 更新出库明细表的供应商信息（从库存继承）
UPDATE soi 
SET supplier_id = inv.supplier_id
FROM stock_out_items soi
JOIN inventories inv ON soi.inventory_id = inv.id
WHERE soi.supplier_id IS NULL AND inv.supplier_id IS NOT NULL;

PRINT '  ✓ 更新出库明细表的供应商信息'

PRINT '================================================'
PRINT '出入库相关表供应商字段完善完成！'
PRINT '================================================'

-- =====================================================
-- 8. 验证结果
-- =====================================================
PRINT '8. 验证结果统计...'

SELECT 
    '入库单' as 表名,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as 供应商覆盖率
FROM stock_ins
UNION ALL
SELECT 
    '入库明细' as 表名,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as 供应商覆盖率
FROM stock_in_items
UNION ALL
SELECT 
    '库存记录' as 表名,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as 供应商覆盖率
FROM inventories
UNION ALL
SELECT 
    '出库单' as 表名,
    COUNT(*) as 总记录数,
    COUNT(main_supplier_id) as 有供应商记录数,
    CAST(COUNT(main_supplier_id) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as 供应商覆盖率
FROM stock_outs
UNION ALL
SELECT 
    '出库明细' as 表名,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as 供应商覆盖率
FROM stock_out_items;

PRINT '验证完成！请查看上方统计结果。'
