-- =====================================================
-- 第二步：添加约束、索引并更新数据
-- 数据库: StudentsCMSSP
-- 请先执行 fix_missing_supplier_fields_step1.sql
-- =====================================================

USE [StudentsCMSSP]
GO

PRINT '第二步：添加约束、索引并更新数据...'
PRINT '================================================'

-- =====================================================
-- 1. 验证前置条件
-- =====================================================
PRINT '1. 验证前置条件...'

-- 检查必需的字段是否存在
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id')
BEGIN
    PRINT '  ❌ 错误：stock_outs.main_supplier_id 字段不存在'
    PRINT '  请先执行 fix_missing_supplier_fields_step1.sql'
    RETURN;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id')
BEGIN
    PRINT '  ❌ 错误：stock_out_items.supplier_id 字段不存在'
    PRINT '  请先执行 fix_missing_supplier_fields_step1.sql'
    RETURN;
END

PRINT '  ✓ 前置条件验证通过'

-- =====================================================
-- 2. 添加外键约束
-- =====================================================
PRINT '2. 添加外键约束...'

-- 添加 stock_outs.main_supplier_id 外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_outs_main_supplier_id')
BEGIN
    ALTER TABLE stock_outs ADD CONSTRAINT FK_stock_outs_main_supplier_id 
        FOREIGN KEY (main_supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 stock_outs.main_supplier_id 外键约束'
END
ELSE
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 外键约束已存在'
END

-- 添加 stock_out_items.supplier_id 外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_out_items_supplier_id')
BEGIN
    ALTER TABLE stock_out_items ADD CONSTRAINT FK_stock_out_items_supplier_id 
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 stock_out_items.supplier_id 外键约束'
END
ELSE
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 外键约束已存在'
END

-- =====================================================
-- 3. 创建索引
-- =====================================================
PRINT '3. 创建索引...'

-- 创建 stock_outs.main_supplier_id 索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_outs_main_supplier_id' AND object_id = OBJECT_ID('stock_outs'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_stock_outs_main_supplier_id ON stock_outs(main_supplier_id);
    PRINT '  ✓ 创建 stock_outs.main_supplier_id 索引'
END
ELSE
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 索引已存在'
END

-- 创建 stock_out_items.supplier_id 索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_out_items_supplier_id' AND object_id = OBJECT_ID('stock_out_items'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_stock_out_items_supplier_id ON stock_out_items(supplier_id);
    PRINT '  ✓ 创建 stock_out_items.supplier_id 索引'
END
ELSE
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 索引已存在'
END

-- =====================================================
-- 4. 更新现有数据
-- =====================================================
PRINT '4. 更新现有数据...'

-- 更新出库明细的供应商信息（从库存继承）
UPDATE soi 
SET supplier_id = inv.supplier_id
FROM stock_out_items soi
JOIN inventories inv ON soi.inventory_id = inv.id
WHERE soi.supplier_id IS NULL AND inv.supplier_id IS NOT NULL;

DECLARE @UpdatedStockOutItems INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedStockOutItems AS VARCHAR) + ' 条出库明细的供应商信息';

-- 计算并更新出库单的主要供应商
WITH MainSupplierCTE AS (
    SELECT 
        soi.stock_out_id,
        soi.supplier_id,
        SUM(soi.quantity) as supplier_quantity,
        SUM(SUM(soi.quantity)) OVER (PARTITION BY soi.stock_out_id) as total_quantity,
        ROW_NUMBER() OVER (PARTITION BY soi.stock_out_id ORDER BY SUM(soi.quantity) DESC) as rn
    FROM stock_out_items soi
    WHERE soi.supplier_id IS NOT NULL
    GROUP BY soi.stock_out_id, soi.supplier_id
)
UPDATE so 
SET main_supplier_id = ms.supplier_id
FROM stock_outs so
JOIN MainSupplierCTE ms ON so.id = ms.stock_out_id
WHERE ms.rn = 1 
AND ms.supplier_quantity >= ms.total_quantity * 0.7  -- 只有当某个供应商占比超过70%时
AND so.main_supplier_id IS NULL;

DECLARE @UpdatedStockOuts INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedStockOuts AS VARCHAR) + ' 条出库单的主要供应商信息';

-- =====================================================
-- 5. 处理平账入库专用供应商
-- =====================================================
PRINT '5. 处理平账入库专用供应商...'

DECLARE @BalanceSupplierID INT;
SELECT @BalanceSupplierID = id FROM suppliers WHERE name = '平账入库专用';

IF @BalanceSupplierID IS NULL
BEGIN
    INSERT INTO suppliers (
        name, contact_person, phone, address, 
        business_license, legal_representative,
        status, created_at, updated_at
    )
    VALUES (
        '平账入库专用', '系统管理员', '000-0000-0000', '系统虚拟地址',
        'VIRTUAL-BALANCE', '系统',
        '正常', GETDATE(), GETDATE()
    );
    
    SET @BalanceSupplierID = SCOPE_IDENTITY();
    PRINT '  ✓ 创建虚拟供应商：平账入库专用 (ID: ' + CAST(@BalanceSupplierID AS VARCHAR) + ')';
    
    -- 更新平账入库的供应商信息
    UPDATE stock_ins 
    SET supplier_id = @BalanceSupplierID
    WHERE stock_in_type = '平账入库' AND supplier_id IS NULL;
    
    DECLARE @UpdatedBalanceStockIns INT = @@ROWCOUNT;
    PRINT '  ✓ 更新了 ' + CAST(@UpdatedBalanceStockIns AS VARCHAR) + ' 条平账入库的供应商信息';
END
ELSE
BEGIN
    PRINT '  ✓ 虚拟供应商已存在 (ID: ' + CAST(@BalanceSupplierID AS VARCHAR) + ')';
END

-- =====================================================
-- 6. 验证结果
-- =====================================================
PRINT '6. 验证结果...'

-- 检查数据覆盖率
SELECT 
    '数据覆盖率' as 检查类型,
    '出库单主要供应商' as 项目,
    COUNT(*) as 总记录数,
    COUNT(main_supplier_id) as 有供应商记录数,
    CAST(COUNT(main_supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 覆盖率百分比
FROM stock_outs
UNION ALL
SELECT 
    '数据覆盖率' as 检查类型,
    '出库明细供应商' as 项目,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 覆盖率百分比
FROM stock_out_items;

PRINT '================================================'
PRINT '✅ 第二步完成：供应商字段修复成功！'
PRINT ''
PRINT '修复摘要：'
PRINT '- 添加了外键约束和索引'
PRINT '- 更新了现有数据的供应商关联'
PRINT '- 处理了平账入库的特殊情况'
PRINT '- 数据覆盖率统计已显示在上方'
PRINT '================================================'
