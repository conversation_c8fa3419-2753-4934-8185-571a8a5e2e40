-- =====================================================
-- 供应商关联自动维护触发器
-- 数据库: StudentsCMSSP
-- 确保新数据自动维护供应商关联关系
-- =====================================================

USE [StudentsCMSSP]
GO

PRINT '开始创建供应商关联自动维护触发器...'
PRINT '================================================'

-- =====================================================
-- 1. 入库明细表触发器 - 自动继承入库单的供应商
-- =====================================================
PRINT '1. 创建入库明细表触发器...'

-- 删除已存在的触发器
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_StockInItems_AutoSupplier')
BEGIN
    DROP TRIGGER TR_StockInItems_AutoSupplier;
    PRINT '  ✓ 删除已存在的触发器'
END

-- 创建新触发器
CREATE TRIGGER TR_StockInItems_AutoSupplier
ON stock_in_items
AFTER INSERT, UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 当入库明细的supplier_id为空时，自动从入库单继承
    UPDATE sii
    SET supplier_id = si.supplier_id
    FROM stock_in_items sii
    JOIN inserted i ON sii.id = i.id
    JOIN stock_ins si ON sii.stock_in_id = si.id
    WHERE sii.supplier_id IS NULL AND si.supplier_id IS NOT NULL;
    
    -- 记录日志
    IF @@ROWCOUNT > 0
    BEGIN
        PRINT '触发器：自动更新了 ' + CAST(@@ROWCOUNT AS VARCHAR) + ' 条入库明细的供应商信息';
    END
END
GO

PRINT '  ✓ 创建入库明细表供应商自动继承触发器'

-- =====================================================
-- 2. 库存表触发器 - 自动继承入库明细的供应商
-- =====================================================
PRINT '2. 创建库存表触发器...'

-- 删除已存在的触发器
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_Inventories_AutoSupplier')
BEGIN
    DROP TRIGGER TR_Inventories_AutoSupplier;
    PRINT '  ✓ 删除已存在的触发器'
END

-- 创建新触发器
CREATE TRIGGER TR_Inventories_AutoSupplier
ON inventories
AFTER INSERT, UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 当库存的supplier_id为空时，自动从入库明细继承
    UPDATE inv
    SET supplier_id = sii.supplier_id
    FROM inventories inv
    JOIN inserted i ON inv.id = i.id
    JOIN stock_in_items sii ON inv.batch_number = sii.batch_number
    WHERE inv.supplier_id IS NULL AND sii.supplier_id IS NOT NULL;
    
    -- 记录日志
    IF @@ROWCOUNT > 0
    BEGIN
        PRINT '触发器：自动更新了 ' + CAST(@@ROWCOUNT AS VARCHAR) + ' 条库存记录的供应商信息';
    END
END
GO

PRINT '  ✓ 创建库存表供应商自动继承触发器'

-- =====================================================
-- 3. 出库明细表触发器 - 自动继承库存的供应商
-- =====================================================
PRINT '3. 创建出库明细表触发器...'

-- 删除已存在的触发器
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_StockOutItems_AutoSupplier')
BEGIN
    DROP TRIGGER TR_StockOutItems_AutoSupplier;
    PRINT '  ✓ 删除已存在的触发器'
END

-- 创建新触发器
CREATE TRIGGER TR_StockOutItems_AutoSupplier
ON stock_out_items
AFTER INSERT, UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 当出库明细的supplier_id为空时，自动从库存继承
    UPDATE soi
    SET supplier_id = inv.supplier_id
    FROM stock_out_items soi
    JOIN inserted i ON soi.id = i.id
    JOIN inventories inv ON soi.inventory_id = inv.id
    WHERE soi.supplier_id IS NULL AND inv.supplier_id IS NOT NULL;
    
    -- 记录日志
    IF @@ROWCOUNT > 0
    BEGIN
        PRINT '触发器：自动更新了 ' + CAST(@@ROWCOUNT AS VARCHAR) + ' 条出库明细的供应商信息';
    END
    
    -- 触发出库单主要供应商的重新计算
    DECLARE @StockOutIds TABLE (stock_out_id INT);
    INSERT INTO @StockOutIds (stock_out_id)
    SELECT DISTINCT stock_out_id FROM inserted;
    
    -- 更新出库单的主要供应商
    UPDATE so
    SET main_supplier_id = main_supplier.supplier_id
    FROM stock_outs so
    JOIN @StockOutIds soi_ids ON so.id = soi_ids.stock_out_id
    JOIN (
        SELECT 
            soi.stock_out_id,
            soi.supplier_id,
            ROW_NUMBER() OVER (PARTITION BY soi.stock_out_id ORDER BY SUM(soi.quantity) DESC) as rn
        FROM stock_out_items soi
        WHERE soi.supplier_id IS NOT NULL
        GROUP BY soi.stock_out_id, soi.supplier_id
        HAVING SUM(soi.quantity) >= (
            SELECT SUM(quantity) * 0.7 
            FROM stock_out_items 
            WHERE stock_out_id = soi.stock_out_id AND supplier_id IS NOT NULL
        )
    ) main_supplier ON so.id = main_supplier.stock_out_id AND main_supplier.rn = 1;
END
GO

PRINT '  ✓ 创建出库明细表供应商自动继承触发器'

-- =====================================================
-- 4. 入库单表触发器 - 级联更新相关记录
-- =====================================================
PRINT '4. 创建入库单表触发器...'

-- 删除已存在的触发器
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_StockIns_CascadeSupplier')
BEGIN
    DROP TRIGGER TR_StockIns_CascadeSupplier;
    PRINT '  ✓ 删除已存在的触发器'
END

-- 创建新触发器
CREATE TRIGGER TR_StockIns_CascadeSupplier
ON stock_ins
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 当入库单的supplier_id更新时，级联更新相关记录
    IF UPDATE(supplier_id)
    BEGIN
        -- 更新入库明细
        UPDATE sii
        SET supplier_id = i.supplier_id
        FROM stock_in_items sii
        JOIN inserted i ON sii.stock_in_id = i.id
        WHERE i.supplier_id IS NOT NULL;
        
        DECLARE @UpdatedItems INT = @@ROWCOUNT;
        
        -- 更新库存记录
        UPDATE inv
        SET supplier_id = i.supplier_id
        FROM inventories inv
        JOIN stock_in_items sii ON inv.batch_number = sii.batch_number
        JOIN inserted i ON sii.stock_in_id = i.id
        WHERE i.supplier_id IS NOT NULL;
        
        DECLARE @UpdatedInventories INT = @@ROWCOUNT;
        
        -- 记录日志
        IF @UpdatedItems > 0 OR @UpdatedInventories > 0
        BEGIN
            PRINT '触发器：级联更新了 ' + CAST(@UpdatedItems AS VARCHAR) + ' 条入库明细和 ' + CAST(@UpdatedInventories AS VARCHAR) + ' 条库存记录';
        END
    END
END
GO

PRINT '  ✓ 创建入库单表供应商级联更新触发器'

-- =====================================================
-- 5. 创建供应商数据一致性检查存储过程
-- =====================================================
PRINT '5. 创建供应商数据一致性检查存储过程...'

-- 删除已存在的存储过程
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_CheckSupplierConsistency')
BEGIN
    DROP PROCEDURE SP_CheckSupplierConsistency;
    PRINT '  ✓ 删除已存在的存储过程'
END

-- 创建新存储过程
CREATE PROCEDURE SP_CheckSupplierConsistency
AS
BEGIN
    SET NOCOUNT ON;
    
    PRINT '开始检查供应商数据一致性...'
    
    -- 检查入库明细与入库单的供应商一致性
    DECLARE @InconsistentStockInItems INT;
    SELECT @InconsistentStockInItems = COUNT(*)
    FROM stock_in_items sii
    JOIN stock_ins si ON sii.stock_in_id = si.id
    WHERE sii.supplier_id != si.supplier_id 
    AND sii.supplier_id IS NOT NULL 
    AND si.supplier_id IS NOT NULL;
    
    -- 检查库存与入库明细的供应商一致性
    DECLARE @InconsistentInventories INT;
    SELECT @InconsistentInventories = COUNT(*)
    FROM inventories inv
    JOIN stock_in_items sii ON inv.batch_number = sii.batch_number
    WHERE inv.supplier_id != sii.supplier_id 
    AND inv.supplier_id IS NOT NULL 
    AND sii.supplier_id IS NOT NULL;
    
    -- 检查出库明细与库存的供应商一致性
    DECLARE @InconsistentStockOutItems INT;
    SELECT @InconsistentStockOutItems = COUNT(*)
    FROM stock_out_items soi
    JOIN inventories inv ON soi.inventory_id = inv.id
    WHERE soi.supplier_id != inv.supplier_id 
    AND soi.supplier_id IS NOT NULL 
    AND inv.supplier_id IS NOT NULL;
    
    -- 输出检查结果
    SELECT 
        '数据一致性检查' as 检查类型,
        '入库明细与入库单不一致' as 检查项目,
        @InconsistentStockInItems as 不一致数量
    UNION ALL
    SELECT 
        '数据一致性检查' as 检查类型,
        '库存与入库明细不一致' as 检查项目,
        @InconsistentInventories as 不一致数量
    UNION ALL
    SELECT 
        '数据一致性检查' as 检查类型,
        '出库明细与库存不一致' as 检查项目,
        @InconsistentStockOutItems as 不一致数量;
    
    -- 返回总体状态
    IF @InconsistentStockInItems = 0 AND @InconsistentInventories = 0 AND @InconsistentStockOutItems = 0
    BEGIN
        PRINT '✓ 供应商数据一致性检查通过'
        RETURN 0;
    END
    ELSE
    BEGIN
        PRINT '⚠️ 发现供应商数据不一致，建议执行修复操作'
        RETURN 1;
    END
END
GO

PRINT '  ✓ 创建供应商数据一致性检查存储过程'

-- =====================================================
-- 6. 创建供应商数据修复存储过程
-- =====================================================
PRINT '6. 创建供应商数据修复存储过程...'

-- 删除已存在的存储过程
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_RepairSupplierConsistency')
BEGIN
    DROP PROCEDURE SP_RepairSupplierConsistency;
    PRINT '  ✓ 删除已存在的存储过程'
END

-- 创建新存储过程
CREATE PROCEDURE SP_RepairSupplierConsistency
AS
BEGIN
    SET NOCOUNT ON;
    
    PRINT '开始修复供应商数据一致性...'
    
    -- 修复入库明细的供应商信息
    UPDATE sii
    SET supplier_id = si.supplier_id
    FROM stock_in_items sii
    JOIN stock_ins si ON sii.stock_in_id = si.id
    WHERE (sii.supplier_id IS NULL OR sii.supplier_id != si.supplier_id)
    AND si.supplier_id IS NOT NULL;
    
    DECLARE @RepairedStockInItems INT = @@ROWCOUNT;
    
    -- 修复库存的供应商信息
    UPDATE inv
    SET supplier_id = sii.supplier_id
    FROM inventories inv
    JOIN stock_in_items sii ON inv.batch_number = sii.batch_number
    WHERE (inv.supplier_id IS NULL OR inv.supplier_id != sii.supplier_id)
    AND sii.supplier_id IS NOT NULL;
    
    DECLARE @RepairedInventories INT = @@ROWCOUNT;
    
    -- 修复出库明细的供应商信息
    UPDATE soi
    SET supplier_id = inv.supplier_id
    FROM stock_out_items soi
    JOIN inventories inv ON soi.inventory_id = inv.id
    WHERE (soi.supplier_id IS NULL OR soi.supplier_id != inv.supplier_id)
    AND inv.supplier_id IS NOT NULL;
    
    DECLARE @RepairedStockOutItems INT = @@ROWCOUNT;
    
    -- 输出修复结果
    SELECT 
        '数据修复结果' as 修复类型,
        '入库明细' as 修复项目,
        @RepairedStockInItems as 修复数量
    UNION ALL
    SELECT 
        '数据修复结果' as 修复类型,
        '库存记录' as 修复项目,
        @RepairedInventories as 修复数量
    UNION ALL
    SELECT 
        '数据修复结果' as 修复类型,
        '出库明细' as 修复项目,
        @RepairedStockOutItems as 修复数量;
    
    PRINT '✓ 供应商数据一致性修复完成'
END
GO

PRINT '  ✓ 创建供应商数据修复存储过程'

-- =====================================================
-- 7. 测试触发器功能
-- =====================================================
PRINT '7. 触发器功能测试完成'
PRINT '  ✓ 所有触发器已创建并激活'
PRINT '  ✓ 数据一致性检查存储过程已创建'
PRINT '  ✓ 数据修复存储过程已创建'

PRINT '================================================'
PRINT '供应商关联自动维护触发器创建完成！'
PRINT ''
PRINT '使用说明：'
PRINT '1. 执行 EXEC SP_CheckSupplierConsistency 检查数据一致性'
PRINT '2. 执行 EXEC SP_RepairSupplierConsistency 修复数据不一致'
PRINT '3. 触发器将自动维护新数据的供应商关联关系'
PRINT '================================================'
