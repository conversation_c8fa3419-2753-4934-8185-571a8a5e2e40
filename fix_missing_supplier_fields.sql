-- =====================================================
-- 修复缺失的供应商字段
-- 数据库: StudentsCMSSP
-- 解决之前对话中发现的字段缺失问题
-- =====================================================

USE [StudentsCMSSP]
GO

PRINT '开始修复缺失的供应商字段...'
PRINT '================================================'

-- =====================================================
-- 1. 检查当前字段状态
-- =====================================================
PRINT '1. 检查当前字段状态...'

-- 检查 stock_outs 表是否有 main_supplier_id 字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id')
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 字段已存在'
END
ELSE
BEGIN
    PRINT '  ❌ stock_outs.main_supplier_id 字段缺失'
END

-- 检查 stock_out_items 表是否有 supplier_id 字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id')
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 字段已存在'
END
ELSE
BEGIN
    PRINT '  ❌ stock_out_items.supplier_id 字段缺失'
END

-- =====================================================
-- 2. 添加缺失的字段
-- =====================================================
PRINT '2. 添加缺失的字段...'

-- 添加 stock_outs.main_supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id')
BEGIN
    ALTER TABLE stock_outs ADD main_supplier_id INT NULL;
    PRINT '  ✓ 添加 main_supplier_id 字段到 stock_outs 表'
END

-- 添加 stock_out_items.supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_out_items ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 stock_out_items 表'
END

-- =====================================================
-- 3. 添加外键约束
-- =====================================================
PRINT '3. 添加外键约束...'

-- 添加 stock_outs.main_supplier_id 外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_outs_main_supplier_id')
BEGIN
    ALTER TABLE stock_outs ADD CONSTRAINT FK_stock_outs_main_supplier_id 
        FOREIGN KEY (main_supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 stock_outs.main_supplier_id 外键约束'
END

-- 添加 stock_out_items.supplier_id 外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_out_items_supplier_id')
BEGIN
    ALTER TABLE stock_out_items ADD CONSTRAINT FK_stock_out_items_supplier_id 
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
    PRINT '  ✓ 添加 stock_out_items.supplier_id 外键约束'
END

-- =====================================================
-- 4. 创建索引
-- =====================================================
PRINT '4. 创建索引...'

-- 创建 stock_outs.main_supplier_id 索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_outs_main_supplier_id' AND object_id = OBJECT_ID('stock_outs'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_stock_outs_main_supplier_id ON stock_outs(main_supplier_id);
    PRINT '  ✓ 创建 stock_outs.main_supplier_id 索引'
END

-- 创建 stock_out_items.supplier_id 索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_out_items_supplier_id' AND object_id = OBJECT_ID('stock_out_items'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_stock_out_items_supplier_id ON stock_out_items(supplier_id);
    PRINT '  ✓ 创建 stock_out_items.supplier_id 索引'
END

-- =====================================================
-- 5. 更新现有数据
-- =====================================================
PRINT '5. 更新现有数据...'

-- 更新出库明细的供应商信息（从库存继承）
UPDATE soi 
SET supplier_id = inv.supplier_id
FROM stock_out_items soi
JOIN inventories inv ON soi.inventory_id = inv.id
WHERE soi.supplier_id IS NULL AND inv.supplier_id IS NOT NULL;

DECLARE @UpdatedStockOutItems INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedStockOutItems AS VARCHAR) + ' 条出库明细的供应商信息'

-- 计算并更新出库单的主要供应商
WITH MainSupplierCTE AS (
    SELECT 
        soi.stock_out_id,
        soi.supplier_id,
        SUM(soi.quantity) as supplier_quantity,
        SUM(SUM(soi.quantity)) OVER (PARTITION BY soi.stock_out_id) as total_quantity,
        ROW_NUMBER() OVER (PARTITION BY soi.stock_out_id ORDER BY SUM(soi.quantity) DESC) as rn
    FROM stock_out_items soi
    WHERE soi.supplier_id IS NOT NULL
    GROUP BY soi.stock_out_id, soi.supplier_id
)
UPDATE so 
SET main_supplier_id = ms.supplier_id
FROM stock_outs so
JOIN MainSupplierCTE ms ON so.id = ms.stock_out_id
WHERE ms.rn = 1 
AND ms.supplier_quantity >= ms.total_quantity * 0.7  -- 只有当某个供应商占比超过70%时
AND so.main_supplier_id IS NULL;

DECLARE @UpdatedStockOuts INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedStockOuts AS VARCHAR) + ' 条出库单的主要供应商信息'

-- =====================================================
-- 6. 验证结果
-- =====================================================
PRINT '6. 验证结果...'

-- 检查字段是否成功添加
SELECT 
    '字段检查' as 检查类型,
    'stock_outs.main_supplier_id' as 字段名,
    CASE WHEN EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id') 
         THEN '存在' ELSE '缺失' END as 状态
UNION ALL
SELECT 
    '字段检查' as 检查类型,
    'stock_out_items.supplier_id' as 字段名,
    CASE WHEN EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id') 
         THEN '存在' ELSE '缺失' END as 状态;

-- 检查数据覆盖率
SELECT 
    '数据覆盖率' as 检查类型,
    '出库单主要供应商' as 项目,
    CAST(COUNT(main_supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 覆盖率百分比
FROM stock_outs
UNION ALL
SELECT 
    '数据覆盖率' as 检查类型,
    '出库明细供应商' as 项目,
    CAST(COUNT(supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 覆盖率百分比
FROM stock_out_items;

-- =====================================================
-- 7. 创建平账入库专用供应商（如果不存在）
-- =====================================================
PRINT '7. 处理平账入库专用供应商...'

DECLARE @BalanceSupplierID INT;
SELECT @BalanceSupplierID = id FROM suppliers WHERE name = '平账入库专用';

IF @BalanceSupplierID IS NULL
BEGIN
    INSERT INTO suppliers (
        name, contact_person, phone, address, 
        business_license, legal_representative,
        status, created_at, updated_at
    )
    VALUES (
        '平账入库专用', '系统管理员', '000-0000-0000', '系统虚拟地址',
        'VIRTUAL-BALANCE', '系统',
        '正常', GETDATE(), GETDATE()
    );
    
    SET @BalanceSupplierID = SCOPE_IDENTITY();
    PRINT '  ✓ 创建虚拟供应商：平账入库专用 (ID: ' + CAST(@BalanceSupplierID AS VARCHAR) + ')'
    
    -- 更新平账入库的供应商信息
    UPDATE stock_ins 
    SET supplier_id = @BalanceSupplierID
    WHERE stock_in_type = '平账入库' AND supplier_id IS NULL;
    
    DECLARE @UpdatedBalanceStockIns INT = @@ROWCOUNT;
    PRINT '  ✓ 更新了 ' + CAST(@UpdatedBalanceStockIns AS VARCHAR) + ' 条平账入库的供应商信息'
END
ELSE
BEGIN
    PRINT '  ✓ 虚拟供应商已存在 (ID: ' + CAST(@BalanceSupplierID AS VARCHAR) + ')'
END

PRINT '================================================'
PRINT '缺失供应商字段修复完成！'
PRINT ''
PRINT '修复摘要：'
PRINT '- 添加了 stock_outs.main_supplier_id 字段'
PRINT '- 添加了 stock_out_items.supplier_id 字段'
PRINT '- 创建了相应的外键约束和索引'
PRINT '- 更新了现有数据的供应商关联'
PRINT '- 处理了平账入库的特殊情况'
PRINT '================================================'
