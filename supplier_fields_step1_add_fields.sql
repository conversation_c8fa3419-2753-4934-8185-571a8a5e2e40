-- =====================================================
-- 第一步：添加供应商字段到出入库相关表
-- 数据库: StudentsCMSSP
-- 执行前请备份数据库
-- =====================================================

USE [StudentsCMSSP]
GO

PRINT '第一步：开始添加供应商字段...'
PRINT '================================================'

-- =====================================================
-- 1. stock_ins 表（入库单表）
-- =====================================================
PRINT '1. 处理 stock_ins 表...'

-- 检查并添加 supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_ins ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 stock_ins 表'
END
ELSE
BEGIN
    PRINT '  ✓ stock_ins.supplier_id 字段已存在'
END

-- =====================================================
-- 2. stock_in_items 表（入库明细表）
-- =====================================================
PRINT '2. 处理 stock_in_items 表...'

-- 检查并添加 supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_in_items') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_in_items ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 stock_in_items 表'
END
ELSE
BEGIN
    PRINT '  ✓ stock_in_items.supplier_id 字段已存在'
END

-- =====================================================
-- 3. inventories 表（库存表）
-- =====================================================
PRINT '3. 处理 inventories 表...'

-- 检查并添加 supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inventories') AND name = 'supplier_id')
BEGIN
    ALTER TABLE inventories ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 inventories 表'
END
ELSE
BEGIN
    PRINT '  ✓ inventories.supplier_id 字段已存在'
END

-- =====================================================
-- 4. stock_outs 表（出库单表）
-- =====================================================
PRINT '4. 处理 stock_outs 表...'

-- 检查并添加 main_supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id')
BEGIN
    ALTER TABLE stock_outs ADD main_supplier_id INT NULL;
    PRINT '  ✓ 添加 main_supplier_id 字段到 stock_outs 表'
END
ELSE
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 字段已存在'
END

-- =====================================================
-- 5. stock_out_items 表（出库明细表）
-- =====================================================
PRINT '5. 处理 stock_out_items 表...'

-- 检查并添加 supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_out_items ADD supplier_id INT NULL;
    PRINT '  ✓ 添加 supplier_id 字段到 stock_out_items 表'
END
ELSE
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 字段已存在'
END

PRINT '================================================'
PRINT '第一步完成：所有供应商字段已添加'
PRINT '请继续执行第二步：supplier_fields_step2_add_constraints.sql'
PRINT '================================================'
