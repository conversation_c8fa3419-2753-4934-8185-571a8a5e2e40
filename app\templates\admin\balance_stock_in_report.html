{% extends "base.html" %}

{% block title %}平账入库报告{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-balance-scale"></i> 平账入库报告
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshReport()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 统计概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-balance-scale"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总平账次数</span>
                                    <span class="info-box-number">{{ stats.total_count or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-exclamation-triangle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">本月平账次数</span>
                                    <span class="info-box-number">{{ stats.month_count or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已消耗完成</span>
                                    <span class="info-box-number">{{ stats.consumed_count or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">涉及供应商</span>
                                    <span class="info-box-number">{{ stats.supplier_count or 0 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 平账入库列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>入库单号</th>
                                    <th>食材名称</th>
                                    <th>平账数量</th>
                                    <th>供应商</th>
                                    <th>关联消耗计划</th>
                                    <th>入库日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in balance_records %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('stock_in.view', id=record.stock_in_id) }}" target="_blank">
                                            {{ record.stock_in_number }}
                                        </a>
                                    </td>
                                    <td>{{ record.ingredient_name }}</td>
                                    <td>
                                        <span class="badge badge-warning">
                                            {{ record.quantity }} {{ record.unit }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if record.supplier_name %}
                                            <span class="text-primary">{{ record.supplier_name }}</span>
                                            {% if record.supplier_name == '平账入库专用' %}
                                                <small class="text-muted">(虚拟)</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">未设置</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if record.consumption_plan_id %}
                                            <a href="{{ url_for('consumption_plan.view', id=record.consumption_plan_id) }}" target="_blank">
                                                计划#{{ record.consumption_plan_id }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ record.stock_in_date|format_datetime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if record.status == '已入库' %}
                                            <span class="badge badge-success">{{ record.status }}</span>
                                        {% elif record.status == '已完成' %}
                                            <span class="badge badge-info">{{ record.status }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ record.status }}</span>
                                        {% endif %}
                                        
                                        {% if record.is_consumed %}
                                            <br><small class="text-success">已消耗</small>
                                        {% else %}
                                            <br><small class="text-warning">未消耗</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('stock_in.view', id=record.stock_in_id) }}" 
                                               class="btn btn-info" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if record.consumption_plan_id %}
                                            <a href="{{ url_for('consumption_plan.view', id=record.consumption_plan_id) }}" 
                                               class="btn btn-primary" target="_blank">
                                                <i class="fas fa-utensils"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-info-circle"></i> 暂无平账入库记录
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination and pagination.pages > 1 %}
                    <nav aria-label="平账入库分页">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.balance_stock_in_report', page=pagination.prev_num) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.balance_stock_in_report', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.balance_stock_in_report', page=pagination.next_num) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    <!-- 说明信息 -->
                    <div class="alert alert-info mt-4">
                        <h5><i class="fas fa-info-circle"></i> 平账入库说明</h5>
                        <ul class="mb-0">
                            <li><strong>平账入库：</strong>当执行消耗计划时库存不足，系统自动生成的虚拟入库记录</li>
                            <li><strong>供应商选择：</strong>优先选择该食材最近入库的供应商，如无记录则使用"平账入库专用"虚拟供应商</li>
                            <li><strong>状态说明：</strong>平账入库直接设为"已入库"状态，并立即被消耗计划消耗</li>
                            <li><strong>管理建议：</strong>频繁的平账入库表明库存管理需要改进，建议加强采购计划和库存监控</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshReport() {
    window.location.reload();
}
</script>
{% endblock %}
