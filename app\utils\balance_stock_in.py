"""
平账入库工具模块 - 用于处理库存不足时自动生成平账入库
"""
from datetime import datetime, timedelta
from sqlalchemy import text
from app import db
from app.models import Inventory, StorageLocation, AuditLog
import json
import uuid

def get_balance_supplier_for_ingredient(ingredient_id, warehouse_id):
    """
    为平账入库选择合适的供应商

    选择逻辑：
    1. 优先选择该食材最近入库的供应商
    2. 如果没有历史记录，选择该仓库区域的默认供应商
    3. 如果都没有，创建"平账入库专用"虚拟供应商

    Args:
        ingredient_id: 食材ID
        warehouse_id: 仓库ID

    Returns:
        int: 供应商ID
    """
    try:
        from app.models import Supplier, StockIn, StockInItem, Warehouse

        # 1. 查找该食材最近入库的供应商
        recent_supplier_query = text("""
        SELECT TOP 1 si.supplier_id
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        WHERE sii.ingredient_id = :ingredient_id
        AND si.warehouse_id = :warehouse_id
        AND si.supplier_id IS NOT NULL
        AND si.stock_in_type != '平账入库'
        ORDER BY si.stock_in_date DESC, si.created_at DESC
        """)

        result = db.session.execute(recent_supplier_query, {
            'ingredient_id': ingredient_id,
            'warehouse_id': warehouse_id
        }).fetchone()

        if result and result.supplier_id:
            return result.supplier_id

        # 2. 查找该仓库区域的默认供应商（最常用的供应商）
        warehouse = Warehouse.query.get(warehouse_id)
        if warehouse and warehouse.area_id:
            default_supplier_query = text("""
            SELECT TOP 1 si.supplier_id, COUNT(*) as usage_count
            FROM stock_ins si
            WHERE si.warehouse_id IN (
                SELECT id FROM warehouses WHERE area_id = :area_id
            )
            AND si.supplier_id IS NOT NULL
            AND si.stock_in_type != '平账入库'
            GROUP BY si.supplier_id
            ORDER BY usage_count DESC
            """)

            result = db.session.execute(default_supplier_query, {
                'area_id': warehouse.area_id
            }).fetchone()

            if result and result.supplier_id:
                return result.supplier_id

        # 3. 创建或获取"平账入库专用"虚拟供应商
        balance_supplier = Supplier.query.filter_by(name='平账入库专用').first()
        if not balance_supplier:
            # 创建虚拟供应商
            balance_supplier_sql = text("""
            INSERT INTO suppliers (
                name, contact_person, phone, address,
                business_license, legal_representative,
                created_at, updated_at
            )
            OUTPUT inserted.id
            VALUES (
                :name, :contact_person, :phone, :address,
                :business_license, :legal_representative,
                GETDATE(), GETDATE()
            )
            """)

            result = db.session.execute(balance_supplier_sql, {
                'name': '平账入库专用',
                'contact_person': '系统管理员',
                'phone': '000-0000-0000',
                'address': '系统虚拟地址',
                'business_license': 'VIRTUAL-BALANCE',
                'legal_representative': '系统'
            })

            supplier_id = result.fetchone()[0]

            # 建立与学校的关系
            if warehouse and warehouse.area_id:
                relation_sql = text("""
                INSERT INTO supplier_school_relations (
                    supplier_id, school_id, contract_number, contract_start_date,
                    contract_end_date, status, created_at, updated_at
                )
                VALUES (
                    :supplier_id, :school_id, :contract_number, GETDATE(),
                    DATEADD(YEAR, 10, GETDATE()), :status, GETDATE(), GETDATE()
                )
                """)

                db.session.execute(relation_sql, {
                    'supplier_id': supplier_id,
                    'school_id': warehouse.area_id,
                    'contract_number': f'BAL-CONTRACT-{warehouse.area_id}',
                    'status': '有效'
                })

            return supplier_id
        else:
            return balance_supplier.id

    except Exception as e:
        from flask import current_app
        current_app.logger.error(f"选择平账入库供应商失败: {str(e)}")
        # 返回None，让调用方处理
        return None

def create_balance_stock_in(ingredient_id, warehouse_id, required_quantity, current_quantity, unit, consumption_plan_id, operator_id):
    """
    创建平账入库记录

    Args:
        ingredient_id: 食材ID
        warehouse_id: 仓库ID
        required_quantity: 需要的数量
        current_quantity: 当前库存数量
        unit: 单位
        consumption_plan_id: 消耗计划ID
        operator_id: 操作员ID

    Returns:
        dict: 包含平账结果的字典
    """
    try:
        # 获取食材名称，用于日志记录
        from app.models import Ingredient, Supplier, Warehouse
        ingredient = Ingredient.query.get(ingredient_id)
        ingredient_name = ingredient.name if ingredient else f"食材ID: {ingredient_id}"

        # 计算差额
        difference = required_quantity - current_quantity

        # 只有在差额为正数时才创建平账入库
        if difference <= 0:
            return {
                'success': True,
                'message': '库存充足，无需平账',
                'difference': 0,
                'ingredient_name': ingredient_name
            }

        # 选择合适的供应商用于平账入库
        supplier_id = get_balance_supplier_for_ingredient(ingredient_id, warehouse_id)

        # 生成批次号 - 使用更明确的格式，包含食材ID
        batch_number = f"BAL-{datetime.now().strftime('%Y%m%d')}-{ingredient_id}-{uuid.uuid4().hex[:6]}"

        # 生成入库单号 - 使用更明确的格式
        stock_in_number = f"RK-BAL-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 记录详细日志
        from flask import current_app
        current_app.logger.info(f"创建平账入库: 食材={ingredient_name}, 需要={required_quantity}, 当前={current_quantity}, 差额={difference}, 批次号={batch_number}")

        # 1. 创建入库单（包含供应商信息）
        sql_stock_in = text("""
        INSERT INTO stock_ins (
            stock_in_number, warehouse_id, stock_in_date, stock_in_type,
            operator_id, supplier_id, status, notes, created_at, updated_at
        )
        OUTPUT inserted.id
        VALUES (
            :stock_in_number, :warehouse_id, GETDATE(), :stock_in_type,
            :operator_id, :supplier_id, :status, :notes, GETDATE(), GETDATE()
        )
        """)

        result = db.session.execute(sql_stock_in, {
            'stock_in_number': stock_in_number,
            'warehouse_id': warehouse_id,
            'stock_in_type': '平账入库',
            'operator_id': operator_id,
            'supplier_id': supplier_id,
            'status': '已入库',  # 平账入库直接设为已入库状态
            'notes': f'系统自动生成的平账入库，关联消耗计划ID: {consumption_plan_id}，供应商ID: {supplier_id}'
        })

        stock_in_id = result.fetchone()[0]

        # 2. 创建入库明细
        sql_stock_in_item = text("""
        INSERT INTO stock_in_items (
            stock_in_id, ingredient_id, storage_location_id, batch_number,
            quantity, unit, production_date, expiry_date, notes,
            quality_status, created_at, updated_at
        )
        OUTPUT inserted.id
        VALUES (
            :stock_in_id, :ingredient_id, :storage_location_id, :batch_number,
            :quantity, :unit, GETDATE(), DATEADD(MONTH, 3, GETDATE()), :notes,
            :quality_status, GETDATE(), GETDATE()
        )
        """)

        # 获取默认存储位置
        storage_location = StorageLocation.query.filter_by(warehouse_id=warehouse_id).first()
        storage_location_id = storage_location.id if storage_location else None

        result = db.session.execute(sql_stock_in_item, {
            'stock_in_id': stock_in_id,
            'ingredient_id': ingredient_id,
            'storage_location_id': storage_location_id,
            'batch_number': batch_number,
            'quantity': difference,
            'unit': unit,
            'notes': '平账入库自动生成',
            'quality_status': '良好'
        })

        stock_in_item_id = result.fetchone()[0]

        # 3. 更新库存
        # 先检查是否已经存在相同批次的库存
        check_inventory_sql = text("""
        SELECT id, quantity FROM inventories
        WHERE batch_number = :batch_number AND ingredient_id = :ingredient_id
        AND warehouse_id = :warehouse_id AND status = '正常'
        """)

        existing_inventory = db.session.execute(check_inventory_sql, {
            'batch_number': batch_number,
            'ingredient_id': ingredient_id,
            'warehouse_id': warehouse_id
        }).fetchone()

        if existing_inventory:
            # 更新现有库存
            update_inventory_sql = text("""
            UPDATE inventories
            SET quantity = quantity + :quantity,
                updated_at = GETDATE(),
                notes = CONCAT(notes, '; 平账入库更新，关联消耗计划ID: ', :consumption_plan_id)
            WHERE id = :id
            """)

            db.session.execute(update_inventory_sql, {
                'quantity': difference,
                'consumption_plan_id': consumption_plan_id,
                'id': existing_inventory.id
            })

            from flask import current_app
            current_app.logger.info(f"更新现有库存: ID={existing_inventory.id}, 原数量={existing_inventory.quantity}, 新增={difference}")
        else:
            # 创建新库存记录（包含供应商信息）
            sql_inventory = text("""
            INSERT INTO inventories (
                warehouse_id, storage_location_id, ingredient_id, batch_number,
                quantity, unit, production_date, expiry_date, status, supplier_id, notes,
                created_at, updated_at
            )
            VALUES (
                :warehouse_id, :storage_location_id, :ingredient_id, :batch_number,
                :quantity, :unit, GETDATE(), DATEADD(MONTH, 3, GETDATE()), :status, :supplier_id, :notes,
                GETDATE(), GETDATE()
            )
            """)

            db.session.execute(sql_inventory, {
                'warehouse_id': warehouse_id,
                'storage_location_id': storage_location_id,
                'ingredient_id': ingredient_id,
                'batch_number': batch_number,
                'quantity': difference,
                'unit': unit,
                'status': '正常',
                'supplier_id': supplier_id,
                'notes': f'平账入库自动生成，关联消耗计划ID: {consumption_plan_id}，供应商ID: {supplier_id}'
            })

            from flask import current_app
            current_app.logger.info(f"创建新库存记录: 批次号={batch_number}, 数量={difference}")

        # 4. 记录审计日志 - 使用原始SQL避免日期时间精度问题
        from app.utils.log_activity import log_activity
        from flask_login import current_user

        # 设置当前用户（如果没有的话）
        if hasattr(current_user, 'id'):
            user_id = current_user.id
        else:
            user_id = operator_id

        # 使用log_activity函数记录审计日志
        log_activity(
            action='balance_stock_in',
            resource_type='consumption_plan',
            resource_id=consumption_plan_id,
            details={
                'ingredient_id': ingredient_id,
                'required_quantity': required_quantity,
                'current_quantity': current_quantity,
                'difference': difference,
                'stock_in_id': stock_in_id,
                'stock_in_item_id': stock_in_item_id
            }
        )
        db.session.commit()

        # 记录详细日志
        from flask import current_app
        current_app.logger.info(f"平账入库完成: 食材={ingredient_name}, 补充数量={difference}, 入库单ID={stock_in_id}")

        return {
            'success': True,
            'message': f'成功创建平账入库，补充库存 {difference} {unit}',
            'stock_in_id': stock_in_id,
            'stock_in_number': stock_in_number,
            'batch_number': batch_number,
            'difference': difference,
            'ingredient_name': ingredient_name
        }

    except Exception as e:
        db.session.rollback()
        # 记录错误日志
        from flask import current_app
        current_app.logger.error(f"创建平账入库失败: {str(e)}")

        return {
            'success': False,
            'message': f'创建平账入库失败: {str(e)}',
            'error': str(e),
            'ingredient_id': ingredient_id,
            'required_quantity': required_quantity,
            'current_quantity': current_quantity
        }
