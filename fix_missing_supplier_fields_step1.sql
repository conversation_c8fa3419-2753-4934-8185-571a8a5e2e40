-- =====================================================
-- 第一步：仅添加缺失的供应商字段
-- 数据库: StudentsCMSSP
-- 安全的分步执行方案
-- =====================================================

USE [StudentsCMSSP]
GO

PRINT '第一步：添加缺失的供应商字段...'
PRINT '================================================'

-- =====================================================
-- 1. 检查当前字段状态
-- =====================================================
PRINT '1. 检查当前字段状态...'

-- 检查 stock_outs 表是否有 main_supplier_id 字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id')
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 字段已存在'
END
ELSE
BEGIN
    PRINT '  ❌ stock_outs.main_supplier_id 字段缺失，需要添加'
END

-- 检查 stock_out_items 表是否有 supplier_id 字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id')
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 字段已存在'
END
ELSE
BEGIN
    PRINT '  ❌ stock_out_items.supplier_id 字段缺失，需要添加'
END

-- =====================================================
-- 2. 添加缺失的字段
-- =====================================================
PRINT '2. 添加缺失的字段...'

-- 添加 stock_outs.main_supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id')
BEGIN
    ALTER TABLE stock_outs ADD main_supplier_id INT NULL;
    PRINT '  ✓ 成功添加 main_supplier_id 字段到 stock_outs 表'
END
ELSE
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 字段已存在，跳过'
END

-- 添加 stock_out_items.supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_out_items ADD supplier_id INT NULL;
    PRINT '  ✓ 成功添加 supplier_id 字段到 stock_out_items 表'
END
ELSE
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 字段已存在，跳过'
END

-- =====================================================
-- 3. 验证字段添加结果
-- =====================================================
PRINT '3. 验证字段添加结果...'

-- 再次检查字段是否成功添加
DECLARE @FieldsAdded INT = 0;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_outs') AND name = 'main_supplier_id')
BEGIN
    PRINT '  ✓ stock_outs.main_supplier_id 字段验证成功'
    SET @FieldsAdded = @FieldsAdded + 1;
END
ELSE
BEGIN
    PRINT '  ❌ stock_outs.main_supplier_id 字段验证失败'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'supplier_id')
BEGIN
    PRINT '  ✓ stock_out_items.supplier_id 字段验证成功'
    SET @FieldsAdded = @FieldsAdded + 1;
END
ELSE
BEGIN
    PRINT '  ❌ stock_out_items.supplier_id 字段验证失败'
END

-- =====================================================
-- 4. 显示当前表结构
-- =====================================================
PRINT '4. 显示当前表结构...'

PRINT '  stock_outs 表的供应商相关字段：'
SELECT 
    COLUMN_NAME as 字段名,
    DATA_TYPE as 数据类型,
    IS_NULLABLE as 允许空值,
    COLUMN_DEFAULT as 默认值
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'stock_outs' 
AND COLUMN_NAME LIKE '%supplier%'
ORDER BY COLUMN_NAME;

PRINT '  stock_out_items 表的供应商相关字段：'
SELECT 
    COLUMN_NAME as 字段名,
    DATA_TYPE as 数据类型,
    IS_NULLABLE as 允许空值,
    COLUMN_DEFAULT as 默认值
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'stock_out_items' 
AND COLUMN_NAME LIKE '%supplier%'
ORDER BY COLUMN_NAME;

-- =====================================================
-- 5. 结果总结
-- =====================================================
PRINT '================================================'
IF @FieldsAdded = 2
BEGIN
    PRINT '✅ 第一步完成：所有字段添加成功！'
    PRINT '请继续执行第二步：fix_missing_supplier_fields_step2.sql'
END
ELSE
BEGIN
    PRINT '❌ 第一步失败：字段添加不完整'
    PRINT '请检查数据库权限或联系管理员'
END
PRINT '================================================'
