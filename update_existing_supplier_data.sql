-- =====================================================
-- 更新现有数据的供应商关联信息
-- 数据库: StudentsCMSSP
-- 执行前请备份数据库
-- =====================================================

USE [StudentsCMSSP]
GO

PRINT '开始更新现有数据的供应商关联信息...'
PRINT '================================================'

-- =====================================================
-- 1. 为平账入库创建或获取虚拟供应商
-- =====================================================
PRINT '1. 处理平账入库的虚拟供应商...'

-- 检查是否存在"平账入库专用"供应商
DECLARE @BalanceSupplierID INT;
SELECT @BalanceSupplierID = id FROM suppliers WHERE name = '平账入库专用';

IF @BalanceSupplierID IS NULL
BEGIN
    -- 创建虚拟供应商
    INSERT INTO suppliers (
        name, contact_person, phone, address, 
        business_license, legal_representative,
        status, created_at, updated_at
    )
    VALUES (
        '平账入库专用', '系统管理员', '000-0000-0000', '系统虚拟地址',
        'VIRTUAL-BALANCE', '系统',
        '正常', GETDATE(), GETDATE()
    );
    
    SET @BalanceSupplierID = SCOPE_IDENTITY();
    PRINT '  ✓ 创建虚拟供应商：平账入库专用 (ID: ' + CAST(@BalanceSupplierID AS VARCHAR) + ')'
END
ELSE
BEGIN
    PRINT '  ✓ 虚拟供应商已存在 (ID: ' + CAST(@BalanceSupplierID AS VARCHAR) + ')'
END

-- =====================================================
-- 2. 更新平账入库的供应商信息
-- =====================================================
PRINT '2. 更新平账入库记录的供应商信息...'

-- 更新平账入库单的供应商
UPDATE stock_ins 
SET supplier_id = @BalanceSupplierID
WHERE stock_in_type = '平账入库' AND supplier_id IS NULL;

DECLARE @UpdatedStockIns INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedStockIns AS VARCHAR) + ' 条平账入库单的供应商信息'

-- 更新平账入库明细的供应商
UPDATE sii 
SET supplier_id = @BalanceSupplierID
FROM stock_in_items sii
JOIN stock_ins si ON sii.stock_in_id = si.id
WHERE si.stock_in_type = '平账入库' AND sii.supplier_id IS NULL;

DECLARE @UpdatedStockInItems INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedStockInItems AS VARCHAR) + ' 条平账入库明细的供应商信息'

-- =====================================================
-- 3. 智能匹配其他入库记录的供应商
-- =====================================================
PRINT '3. 智能匹配其他入库记录的供应商...'

-- 通过采购订单关联更新入库单供应商
UPDATE si 
SET supplier_id = po.supplier_id
FROM stock_ins si
JOIN stock_in_items sii ON si.id = sii.stock_in_id
JOIN purchase_order_items poi ON sii.purchase_order_item_id = poi.id
JOIN purchase_orders po ON poi.purchase_order_id = po.id
WHERE si.supplier_id IS NULL AND po.supplier_id IS NOT NULL;

DECLARE @UpdatedFromPO INT = @@ROWCOUNT;
PRINT '  ✓ 通过采购订单更新了 ' + CAST(@UpdatedFromPO AS VARCHAR) + ' 条入库单的供应商信息'

-- =====================================================
-- 4. 级联更新库存表的供应商信息
-- =====================================================
PRINT '4. 更新库存表的供应商信息...'

-- 从入库明细继承供应商信息到库存
UPDATE inv 
SET supplier_id = sii.supplier_id
FROM inventories inv
JOIN stock_in_items sii ON inv.batch_number = sii.batch_number
WHERE inv.supplier_id IS NULL AND sii.supplier_id IS NOT NULL;

DECLARE @UpdatedInventories INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedInventories AS VARCHAR) + ' 条库存记录的供应商信息'

-- =====================================================
-- 5. 更新出库明细的供应商信息
-- =====================================================
PRINT '5. 更新出库明细的供应商信息...'

-- 从库存记录继承供应商信息到出库明细
UPDATE soi 
SET supplier_id = inv.supplier_id
FROM stock_out_items soi
JOIN inventories inv ON soi.inventory_id = inv.id
WHERE soi.supplier_id IS NULL AND inv.supplier_id IS NOT NULL;

DECLARE @UpdatedStockOutItems INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedStockOutItems AS VARCHAR) + ' 条出库明细的供应商信息'

-- =====================================================
-- 6. 更新出库单的主要供应商信息
-- =====================================================
PRINT '6. 计算并更新出库单的主要供应商...'

-- 创建临时表存储出库单的主要供应商
CREATE TABLE #TempMainSupplier (
    stock_out_id INT,
    main_supplier_id INT,
    supplier_ratio DECIMAL(5,2)
);

-- 计算每个出库单的主要供应商
INSERT INTO #TempMainSupplier (stock_out_id, main_supplier_id, supplier_ratio)
SELECT 
    soi.stock_out_id,
    soi.supplier_id as main_supplier_id,
    CAST(SUM(soi.quantity) * 100.0 / total_quantity.total AS DECIMAL(5,2)) as supplier_ratio
FROM stock_out_items soi
JOIN (
    SELECT 
        stock_out_id,
        SUM(quantity) as total
    FROM stock_out_items 
    WHERE supplier_id IS NOT NULL
    GROUP BY stock_out_id
) total_quantity ON soi.stock_out_id = total_quantity.stock_out_id
WHERE soi.supplier_id IS NOT NULL
GROUP BY soi.stock_out_id, soi.supplier_id, total_quantity.total
HAVING CAST(SUM(soi.quantity) * 100.0 / total_quantity.total AS DECIMAL(5,2)) >= 70.0;

-- 更新出库单的主要供应商（只有当某个供应商占比超过70%时）
UPDATE so 
SET main_supplier_id = tms.main_supplier_id
FROM stock_outs so
JOIN #TempMainSupplier tms ON so.id = tms.stock_out_id
WHERE so.main_supplier_id IS NULL;

DECLARE @UpdatedStockOuts INT = @@ROWCOUNT;
PRINT '  ✓ 更新了 ' + CAST(@UpdatedStockOuts AS VARCHAR) + ' 条出库单的主要供应商信息'

-- 清理临时表
DROP TABLE #TempMainSupplier;

-- =====================================================
-- 7. 数据质量检查
-- =====================================================
PRINT '7. 执行数据质量检查...'

-- 检查是否有孤立的供应商引用
DECLARE @OrphanSupplierRefs INT;

SELECT @OrphanSupplierRefs = COUNT(*)
FROM (
    SELECT supplier_id FROM stock_ins WHERE supplier_id IS NOT NULL AND supplier_id NOT IN (SELECT id FROM suppliers)
    UNION ALL
    SELECT supplier_id FROM stock_in_items WHERE supplier_id IS NOT NULL AND supplier_id NOT IN (SELECT id FROM suppliers)
    UNION ALL
    SELECT supplier_id FROM inventories WHERE supplier_id IS NOT NULL AND supplier_id NOT IN (SELECT id FROM suppliers)
    UNION ALL
    SELECT main_supplier_id FROM stock_outs WHERE main_supplier_id IS NOT NULL AND main_supplier_id NOT IN (SELECT id FROM suppliers)
    UNION ALL
    SELECT supplier_id FROM stock_out_items WHERE supplier_id IS NOT NULL AND supplier_id NOT IN (SELECT id FROM suppliers)
) orphan_refs;

IF @OrphanSupplierRefs > 0
BEGIN
    PRINT '  ⚠️ 发现 ' + CAST(@OrphanSupplierRefs AS VARCHAR) + ' 个孤立的供应商引用，需要手动处理'
END
ELSE
BEGIN
    PRINT '  ✓ 数据完整性检查通过，无孤立引用'
END

-- =====================================================
-- 8. 生成更新报告
-- =====================================================
PRINT '8. 生成更新报告...'

SELECT 
    '更新统计' as 类型,
    '平账入库单' as 项目,
    @UpdatedStockIns as 更新数量
UNION ALL
SELECT 
    '更新统计' as 类型,
    '平账入库明细' as 项目,
    @UpdatedStockInItems as 更新数量
UNION ALL
SELECT 
    '更新统计' as 类型,
    '采购关联入库单' as 项目,
    @UpdatedFromPO as 更新数量
UNION ALL
SELECT 
    '更新统计' as 类型,
    '库存记录' as 项目,
    @UpdatedInventories as 更新数量
UNION ALL
SELECT 
    '更新统计' as 类型,
    '出库明细' as 项目,
    @UpdatedStockOutItems as 更新数量
UNION ALL
SELECT 
    '更新统计' as 类型,
    '出库单主供应商' as 项目,
    @UpdatedStockOuts as 更新数量;

-- =====================================================
-- 9. 最终验证
-- =====================================================
PRINT '9. 最终数据覆盖率验证...'

SELECT 
    '入库单' as 表名,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 供应商覆盖率
FROM stock_ins
UNION ALL
SELECT 
    '入库明细' as 表名,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 供应商覆盖率
FROM stock_in_items
UNION ALL
SELECT 
    '库存记录' as 表名,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 供应商覆盖率
FROM inventories
UNION ALL
SELECT 
    '出库明细' as 表名,
    COUNT(*) as 总记录数,
    COUNT(supplier_id) as 有供应商记录数,
    CAST(COUNT(supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 供应商覆盖率
FROM stock_out_items
UNION ALL
SELECT 
    '出库单' as 表名,
    COUNT(*) as 总记录数,
    COUNT(main_supplier_id) as 有供应商记录数,
    CAST(COUNT(main_supplier_id) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as 供应商覆盖率
FROM stock_outs;

PRINT '================================================'
PRINT '现有数据供应商关联信息更新完成！'
PRINT '================================================'
